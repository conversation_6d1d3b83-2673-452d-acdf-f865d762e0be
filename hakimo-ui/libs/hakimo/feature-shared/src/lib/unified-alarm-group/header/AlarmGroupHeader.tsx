import { Escalation, Location } from '@hakimo-ui/hakimo/types';
import { toast, useCanUpdateAlarmGroupStatus } from '@hakimo-ui/hakimo/util';
import { Button, Label, LiveClock } from '@hakimo-ui/shared/ui-base';
import { StatusStepper } from '@hakimo-ui/hakimo/ui-elements';
import * as Sentry from '@sentry/react';
import { useState } from 'react';
import { useUpdateAlarmGroupState } from '@hakimo-ui/hakimo/data-access';
import MonitoringWindowInfo from '../../monitoring-window-info/MonitoringWindowInfo';
import { CameraFeedMode, EscalationState } from '../types';
import {
  normalizeAlarmGroupStatus,
  mapAlarmGroupStatusToLabelType,
} from '../utils';
import AlarmEscalationBanner from './AlarmEscalationBanner';
import HeaderActions from './HeaderActions';
import MenuActions from './MenuActions';
import ScanEscalationBanner from './ScanEscalationBanner';

interface Props {
  location: Location;
  alarmGroupId: string;
  alarmStatus: string;
  globalCamMode: CameraFeedMode;
  onChangeGlobalCamMode: (mode: CameraFeedMode) => void;
  isFullScreen: boolean;
  toggleFullScreen: () => void;
  escalation?: Escalation;
  scanEscalationState?: EscalationState;
  onResolveEscalation?: (comment?: string) => void;
  isScanAlarmGroup?: boolean;
  resolution?: string;
  product?: 'scan' | 'remote-guarding';
}

export function AlarmGroupHeader(props: Props) {
  const {
    location,
    alarmGroupId,
    alarmStatus,
    globalCamMode,
    onChangeGlobalCamMode,
    isFullScreen,
    toggleFullScreen,
    escalation,
    isScanAlarmGroup,
    scanEscalationState,
    onResolveEscalation,
    resolution,
    product,
  } = props;

  const [isStatusEditing, setIsStatusEditing] = useState(false);
  const canUpdateStatus = useCanUpdateAlarmGroupStatus();
  const resolveAlarmGroupMutation = useUpdateAlarmGroupState(
    alarmGroupId,
    () => {
      toast('Alarm group resolved successfully', { type: 'success' });
      setIsStatusEditing(false);
    }
  );

  const onCopyLink = async () => {
    const linkUrl =
      product === 'scan'
        ? `${window.location.origin}/alarm-groups/${alarmGroupId}`
        : `${window.location.origin}/location-alarms/${alarmGroupId}`;
    const toastMessage =
      product === 'scan'
        ? 'Alarm group link copied to clipboard'
        : 'Location alarm link copied to clipboard';
    try {
      await navigator.clipboard.writeText(linkUrl);
      toast(toastMessage);
    } catch (err) {
      Sentry.captureMessage('Error copying text to clipboard');
    }
  };

  const handleResolveClick = () => {
    setIsStatusEditing(true);
  };
  const onClose = () => {
    setIsStatusEditing(false);
  };

  const onSubmit = (comment: string) => {
    const payload = {
      resolution: 'safe' as const,
      resolutionComment: comment,
    };
    resolveAlarmGroupMutation.mutate(payload);
  };
  const isResolved = ['resolved', 'resolved_by_operator', 'resolved_by_hakimo'].includes(alarmStatus.toLowerCase());
  const showResolveButton = product === 'scan' && !isResolved && canUpdateStatus;

  return (
    <div>
      {product === 'remote-guarding' && escalation && (
        <AlarmEscalationBanner alarmId={alarmGroupId} escalation={escalation} product={product} />
      )}
      {product === 'scan' && resolution === 'escalation_open' && escalation && (
        <AlarmEscalationBanner alarmId={alarmGroupId} escalation={escalation} product={product} />
      )}
      {isScanAlarmGroup && scanEscalationState && onResolveEscalation && (
          <ScanEscalationBanner
            escalationState={scanEscalationState}
            onResolveEscalation={onResolveEscalation}
          />
        )}

      <div
        id="alarm-header"
        className="top-0 flex items-center gap-3 px-4 py-2 relative z-10"
      >
        <div className="flex flex-1 items-center gap-2 overflow-visible">
          {!isStatusEditing && (
            <div className="flex max-w-[60%] flex-shrink-0 flex-col ">
              <span
                className="overflow-hidden text-ellipsis whitespace-nowrap"
                title={`#${alarmGroupId} - ${location?.name}`}
              >
                {location.name}
              </span>
              <div
                title={location.description}
                className="dark:text-ondark-text-2 text-onlight-text-2 overflow-hidden text-ellipsis whitespace-nowrap text-xs"
              >
                {location.description}
              </div>
            </div>
          )}
          {!isStatusEditing && (
            <div className="h-6 border-l border-black/20 dark:border-white/20"></div>
          )}
          <div className="flex flex-1 items-center gap-4 relative z-20">
            {isStatusEditing ? (
              <div className="relative z-30">
                <StatusStepper
                  onSubmit={onSubmit}
                  onClose={onClose}
                  isSubmitInProgress={resolveAlarmGroupMutation.isLoading}
                />
              </div>
            ) : (
              <>
                <Label type={mapAlarmGroupStatusToLabelType(alarmStatus)}>
                  {normalizeAlarmGroupStatus(alarmStatus)}
                </Label>
                {showResolveButton && (
                  <Button
                    title="Resolve"
                    variant="success"
                    onClick={handleResolveClick}
                  >
                    Resolve
                  </Button>
                )}
              </>
            )}
          </div>
        </div>

        <div className="flex-1">
          <span>
            {location.timezone && <LiveClock timezone={location.timezone} />}
          </span>
          <span className="font-mono text-xs">
            <MonitoringWindowInfo locationId={location.id} />
          </span>
        </div>
        {!isStatusEditing && (
          <div className="flex items-center gap-2">
            <HeaderActions
              mode={globalCamMode}
              onChangeMode={onChangeGlobalCamMode}
              locationName={location.name}
            />
            <MenuActions
              isFullScreen={isFullScreen}
              onCopyLink={onCopyLink}
              toggleFullScreen={toggleFullScreen}
            />
          </div>
        )}
      </div>
    </div>
  );
}

export default AlarmGroupHeader;
